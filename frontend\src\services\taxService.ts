import { apiClient } from "../lib/api";
import type {
  Tax,
  CreateTaxRequest,
  UpdateTaxRequest,
  TaxQueryParams,
  ApiResponse,
  PaginatedResponse,
} from "../types/api";

export class TaxService {
  private static readonly BASE_PATH = "/taxes";

  // Vergi oranlarını listele
  static async getTaxes(
    params?: TaxQueryParams,
  ): Promise<ApiResponse<PaginatedResponse<Tax>>> {
    const searchParams = new URLSearchParams();

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value));
        }
      });
    }

    const queryString = searchParams.toString();
    const endpoint = queryString
      ? `${this.BASE_PATH}?${queryString}`
      : this.BASE_PATH;

    return apiClient.get<PaginatedResponse<Tax>>(endpoint);
  }

  // Tüm aktif vergi oranları (select için)
  static async getActiveTaxes(): Promise<ApiResponse<Tax[]>> {
    return apiClient.get<Tax[]>(`${this.BASE_PATH}?active=true&limit=100`);
  }

  // Varsayılan vergi oranı
  static async getDefaultTax(): Promise<ApiResponse<Tax>> {
    return apiClient.get<Tax>(`${this.BASE_PATH}/default`);
  }

  // Tek vergi detayı
  static async getTax(id: string): Promise<ApiResponse<Tax>> {
    return apiClient.get<Tax>(`${this.BASE_PATH}/${id}`);
  }

  // Yeni vergi oranı oluştur
  static async createTax(data: CreateTaxRequest): Promise<ApiResponse<Tax>> {
    return apiClient.post<Tax>(this.BASE_PATH, data);
  }

  // Vergi oranı güncelle
  static async updateTax(
    id: string,
    data: UpdateTaxRequest,
  ): Promise<ApiResponse<Tax>> {
    return apiClient.put<Tax>(`${this.BASE_PATH}/${id}`, data);
  }

  // Vergi oranı sil
  static async deleteTax(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`${this.BASE_PATH}/${id}`);
  }

  // Vergi kodunun benzersizliğini kontrol et
  static async checkCodeUniqueness(
    code: string,
    excludeId?: string,
  ): Promise<ApiResponse<{ isUnique: boolean }>> {
    const params = new URLSearchParams({ code });
    if (excludeId) {
      params.append("excludeId", excludeId);
    }
    return apiClient.get<{ isUnique: boolean }>(
      `${this.BASE_PATH}/check-code?${params.toString()}`,
    );
  }

  // Vergi durumunu değiştir (aktif/pasif)
  static async toggleTaxStatus(id: string): Promise<ApiResponse<Tax>> {
    return apiClient.put<Tax>(`${this.BASE_PATH}/${id}/toggle-status`, {});
  }

  // Varsayılan vergi oranını ayarla
  static async setDefaultTax(id: string): Promise<ApiResponse<Tax>> {
    return apiClient.put<Tax>(`${this.BASE_PATH}/${id}/set-default`, {});
  }

  // Vergi oranını kopyala
  static async duplicateTax(
    id: string,
    newCode: string,
    newName: string,
  ): Promise<ApiResponse<Tax>> {
    return apiClient.post<Tax>(`${this.BASE_PATH}/${id}/duplicate`, {
      code: newCode,
      name: newName,
    });
  }

  // Toplu vergi işlemleri
  static async bulkUpdateStatus(
    ids: string[],
    active: boolean,
  ): Promise<ApiResponse<{ updated: number }>> {
    return apiClient.put<{ updated: number }>(`${this.BASE_PATH}/bulk/status`, {
      ids,
      active,
    });
  }

  static async bulkDelete(
    ids: string[],
  ): Promise<ApiResponse<{ deleted: number }>> {
    return apiClient.post<{ deleted: number }>(
      `${this.BASE_PATH}/bulk/delete`,
      { ids },
    );
  }

  static async bulkUpdateRate(
    ids: string[],
    rate: number,
  ): Promise<ApiResponse<{ updated: number }>> {
    return apiClient.put<{ updated: number }>(`${this.BASE_PATH}/bulk/rate`, {
      ids,
      rate,
    });
  }

  // Vergi hesaplama yardımcıları
  static async calculateTax(
    amount: number,
    taxId: string,
  ): Promise<
    ApiResponse<{
      baseAmount: number;
      taxAmount: number;
      totalAmount: number;
      taxRate: number;
      taxType: string;
    }>
  > {
    return apiClient.post<{
      baseAmount: number;
      taxAmount: number;
      totalAmount: number;
      taxRate: number;
      taxType: string;
    }>(`${this.BASE_PATH}/calculate`, { amount, taxId });
  }

  // Vergi istatistikleri
  static async getTaxStats(): Promise<
    ApiResponse<{
      total: number;
      active: number;
      inactive: number;
      inclusive: number;
      exclusive: number;
      averageRate: number;
    }>
  > {
    return apiClient.get<{
      total: number;
      active: number;
      inactive: number;
      inclusive: number;
      exclusive: number;
      averageRate: number;
    }>(`${this.BASE_PATH}/stats`);
  }

  // Vergi oranı geçmişi
  static async getTaxHistory(id: string): Promise<
    ApiResponse<
      {
        id: string;
        rate: number;
        changedAt: string;
        changedBy: string;
      }[]
    >
  > {
    return apiClient.get<
      {
        id: string;
        rate: number;
        changedAt: string;
        changedBy: string;
      }[]
    >(`${this.BASE_PATH}/${id}/history`);
  }

  // Vergi arama önerileri
  static async getSearchSuggestions(
    query: string,
  ): Promise<ApiResponse<string[]>> {
    return apiClient.get<string[]>(
      `${this.BASE_PATH}/search-suggestions?q=${encodeURIComponent(query)}`,
    );
  }

  // Vergi oranı şablonları (ülke bazlı)
  static async getTaxTemplates(country: string = "TR"): Promise<
    ApiResponse<
      {
        code: string;
        name: string;
        rate: number;
        type: string;
        description: string;
      }[]
    >
  > {
    return apiClient.get<
      {
        code: string;
        name: string;
        rate: number;
        type: string;
        description: string;
      }[]
    >(`${this.BASE_PATH}/templates?country=${country}`);
  }
}
