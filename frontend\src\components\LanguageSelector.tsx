import { useState } from "react";
import { useTranslation } from "react-i18next";
import { Language } from "../lib/types";
import { useSettingsStore } from "../lib/store";
import { LanguageIcon } from "@heroicons/react/24/outline";

export function LanguageSelector() {
  const { t, i18n } = useTranslation();
  const { language, setLanguage } = useSettingsStore();
  const [isOpen, setIsOpen] = useState(false);

  const handleLanguageChange = (newLanguage: Language) => {
    setLanguage(newLanguage);
    i18n.changeLanguage(newLanguage);
    setIsOpen(false);
  };

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="p-2 rounded-full hover:bg-neutral-100 dark:hover:bg-neutral-700 transition-colors"
        aria-label={t("language.changeLanguage")}
      >
        <LanguageIcon className="w-6 h-6 text-neutral-600 dark:text-neutral-300" />
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 z-50 border border-gray-200 dark:border-gray-700">
          <button
            onClick={() => handleLanguageChange(Language.TR)}
            className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${
              language === Language.TR
                ? "bg-blue-50 dark:bg-blue-900 text-blue-600 dark:text-blue-300"
                : "text-gray-700 dark:text-gray-300"
            }`}
          >
            🇹🇷 {t("language.turkish")}
          </button>
          <button
            onClick={() => handleLanguageChange(Language.EN)}
            className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${
              language === Language.EN
                ? "bg-blue-50 dark:bg-blue-900 text-blue-600 dark:text-blue-300"
                : "text-gray-700 dark:text-gray-300"
            }`}
          >
            🇺🇸 {t("language.english")}
          </button>
        </div>
      )}
    </div>
  );
}
