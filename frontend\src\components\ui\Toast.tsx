import React, { useEffect, useState } from "react";
import { createPortal } from "react-dom";
import {
  XMarkIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  XCircleIcon,
} from "@heroicons/react/24/outline";
import { cn } from "../../lib/utils";

export type ToastType = "success" | "error" | "warning" | "info";

export interface ToastProps {
  id: string;
  type: ToastType;
  title: string;
  message?: string;
  duration?: number;
  onClose: (id: string) => void;
}

const Toast: React.FC<ToastProps> = ({
  id,
  type,
  title,
  message,
  duration = 5000,
  onClose,
}) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Animasyon için kısa bir gecikme
    const showTimer = setTimeout(() => setIsVisible(true), 100);

    // Otomatik kapanma
    const hideTimer = setTimeout(() => {
      setIsVisible(false);
      setTimeout(() => onClose(id), 300); // Animasyon tamamlandıktan sonra kaldır
    }, duration);

    return () => {
      clearTimeout(showTimer);
      clearTimeout(hideTimer);
    };
  }, [id, duration, onClose]);

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(() => onClose(id), 300);
  };

  const getIcon = () => {
    switch (type) {
      case "success":
        return <CheckCircleIcon className="w-5 h-5 text-success-500" />;
      case "error":
        return <XCircleIcon className="w-5 h-5 text-error-500" />;
      case "warning":
        return <ExclamationTriangleIcon className="w-5 h-5 text-warning-600" />;
      case "info":
        return <InformationCircleIcon className="w-5 h-5 text-primary-500" />;
    }
  };

  const getBackgroundColor = () => {
    switch (type) {
      case "success":
        return "bg-success-100 dark:bg-success-dark border-success-500";
      case "error":
        return "bg-error-100 dark:bg-error-500/10 border-error-500";
      case "warning":
        return "bg-warning-100 dark:bg-warning-600/10 border-warning-600";
      case "info":
        return "bg-primary-100 dark:bg-primary-500/10 border-primary-500";
    }
  };

  return (
    <div
      className={cn(
        "flex items-start p-4 rounded-lg border shadow-lg transition-all duration-300 transform max-w-sm w-full",
        getBackgroundColor(),
        isVisible ? "translate-x-0 opacity-100" : "translate-x-full opacity-0",
      )}
    >
      <div className="flex-shrink-0">{getIcon()}</div>
      <div className="ml-3 flex-1">
        <p className="text-sm font-medium text-neutral-black dark:text-neutral-white">
          {title}
        </p>
        {message && (
          <p className="mt-1 text-sm text-neutral-600 dark:text-neutral-300">
            {message}
          </p>
        )}
      </div>
      <div className="ml-4 flex-shrink-0">
        <button
          onClick={handleClose}
          className="inline-flex rounded-md p-1.5 hover:bg-neutral-200 dark:hover:bg-neutral-700 transition-colors"
        >
          <XMarkIcon className="w-4 h-4 text-neutral-500 dark:text-neutral-400" />
        </button>
      </div>
    </div>
  );
};

// Toast Container
export interface ToastContainerProps {
  toasts: ToastProps[];
  onClose: (id: string) => void;
}

const ToastContainer: React.FC<ToastContainerProps> = ({ toasts, onClose }) => {
  if (toasts.length === 0) return null;

  return createPortal(
    <div className="fixed top-4 right-4 z-50 space-y-2">
      {toasts.map((toast) => (
        <Toast key={toast.id} {...toast} onClose={onClose} />
      ))}
    </div>,
    document.body,
  );
};

export { Toast, ToastContainer };
