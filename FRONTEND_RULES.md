# 🚨 Frontend Kritik Kurallar

## 🔥 ASLA YAPMA

- ❌ `any` type kullanma
- ❌ `useEffect` içinde state update loop'u
- ❌ Key prop olmadan map()
- ❌ Async function'ı useEffect'e direkt verme
- ❌ State'i direkt mutate etme

## ✅ HER ZAMAN YAP

- ✅ Loading state ekle
- ✅ Error handling yap
- ✅ TypeScript strict mode
- ✅ Component'leri memo() ile sar
- ✅ Custom hook kullan (API calls için)

## 🎯 STATE MANAGEMENT

```javascript
// ❌ YANLIŞ
const [data, setData] = useState();
const [loading, setLoading] = useState();
const [error, setError] = useState();

// ✅ DOĞRU
const { data, loading, error } = useQuery("products", fetchProducts);
```

## 🔧 FORM HANDLING

```javascript
// ❌ YANLIŞ
const [name, setName] = useState("");
const [email, setEmail] = useState("");

// ✅ DOĞRU
const { control, handleSubmit } = useForm({
  resolver: zodResolver(schema),
});
```

## 🎨 STYLING

- ✅ Tailwind classes kullan
- ✅ Dark mode support ekle
- ✅ Responsive design (mobile-first)
- ❌ Inline styles kullanma

## 🚀 PERFORMANCE

- ✅ React.memo() kullan
- ✅ useMemo() heavy calculations için
- ✅ useCallback() event handlers için
- ✅ Lazy loading (React.lazy)

## 🐛 DEBUGGING

- ✅ Console.log'ları production'da kaldır
- ✅ Error boundary kullan
- ✅ React DevTools kullan
- ✅ Network tab'ı kontrol et

## 📁 FOLDER STRUCTURE

```
src/
├── components/     # Reusable UI
├── pages/         # Route components
├── hooks/         # Custom hooks
├── lib/           # Utils, store, api
├── types/         # TypeScript types
└── services/      # API calls
```

## 🔄 API CALLS

```javascript
// ❌ YANLIŞ
useEffect(() => {
  fetch("/api/products").then(setProducts);
}, []);

// ✅ DOĞRU
const { data: products } = useQuery("products", () =>
  apiClient.get("/products"),
);
```

## 🎭 COMPONENT PATTERN

```javascript
// ✅ DOĞRU PATTERN
export const MyComponent = React.memo(({ prop1, prop2 }) => {
  const [loading, setLoading] = useState(false);

  const handleClick = useCallback(() => {
    // logic here
  }, [dependency]);

  if (loading) return <LoadingSpinner />;

  return <div>...</div>;
});
```

## 🚨 EMERGENCY CHECKLIST

1. TypeScript hatası var mı?
2. Console'da error var mı?
3. Network tab'da 404/500 var mı?
4. Loading state eksik mi?
5. Key prop eksik mi?

---

**💡 Kural:** Eğer 5 dakikada çözemezsen, bu dosyayı oku!
