{"name": "atropos-pos", "version": "1.0.0", "description": "Türkiye şartlarına uygun restoran POS uygulaması", "main": "index.js", "scripts": {"install:all": "npm install && cd frontend && npm install && cd ../backend && npm install", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "dev:electron": "cd frontend && npm run electron:dev", "build": "npm run build:backend && npm run build:frontend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "db:generate": "cd backend && npx prisma generate", "db:push": "cd backend && npx prisma db push", "db:studio": "cd backend && npx prisma studio", "db:migrate": "cd backend && npx prisma migrate dev", "db:reset": "cd backend && npx prisma migrate reset", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["pos", "restaurant", "electron", "react", "typescript", "prisma", "postgresql"], "author": "<PERSON>", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2", "globals": "^16.3.0", "prettier": "^3.6.2", "prettier-plugin-organize-imports": "^4.1.0"}, "workspaces": ["frontend", "backend"]}