import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import { useEffect } from "react";
import { Login } from "./pages/Login";
import { Dashboard } from "./pages/Dashboard";
import { ProductManagement } from "./pages/ProductManagement";
import { useAuthStore, useSettingsStore } from "./lib/store";
import { apiClient } from "./lib/api";
import { useAuth } from "./hooks/useAuth";
import { ThemeMode } from "./lib/types";

// Protected Route component
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { isAuthenticated } = useAuthStore();

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  return <>{children}</>;
};

function App() {
  const { token, isAuthenticated } = useAuthStore();
  const { theme } = useSettingsStore();

  // Token geçerliliğini kontrol et
  useAuth();

  // Tema initialization - sayfa yüklendiğinde tema uygula
  useEffect(() => {
    const applyTheme = () => {
      if (theme === ThemeMode.DARK) {
        document.documentElement.classList.add("dark");
      } else {
        document.documentElement.classList.remove("dark");
      }
    };

    // Hemen uygula
    applyTheme();

    // Store'dan tema değişikliklerini dinle
    const unsubscribe = useSettingsStore.subscribe((state) => {
      if (state.theme === ThemeMode.DARK) {
        document.documentElement.classList.add("dark");
      } else {
        document.documentElement.classList.remove("dark");
      }
    });

    return unsubscribe;
  }, [theme]);

  // Uygulama başladığında token'ı restore et
  useEffect(() => {
    if (token) {
      apiClient.setToken(token);
    }
  }, [token]);

  // Store değişikliklerini dinle ve API client'ı güncelle
  useEffect(() => {
    const unsubscribe = useAuthStore.subscribe((state) => {
      if (state.token) {
        apiClient.setToken(state.token);
      } else {
        apiClient.setToken(null);
      }
    });

    return unsubscribe;
  }, []);

  return (
    <Router>
      <Routes>
        <Route
          path="/login"
          element={
            isAuthenticated ? <Navigate to="/dashboard" replace /> : <Login />
          }
        />
        <Route
          path="/dashboard"
          element={
            <ProtectedRoute>
              <Dashboard />
            </ProtectedRoute>
          }
        />
        <Route
          path="/products"
          element={
            <ProtectedRoute>
              <ProductManagement />
            </ProtectedRoute>
          }
        />
        <Route
          path="/"
          element={
            <Navigate to={isAuthenticated ? "/dashboard" : "/login"} replace />
          }
        />
      </Routes>
    </Router>
  );
}

export default App;
