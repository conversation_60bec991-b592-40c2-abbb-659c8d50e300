import { z } from "zod";
import { ProductUnit } from "../types/api";

const productFormSchemaDefinition = z.object({
  // Temel bilgiler
  name: z
    .string()
    .min(2, "Ürün adı en az 2 karakter olmalıdır")
    .max(200, "Ürün adı en fazla 200 karakter olabilir")
    .trim(),

  code: z
    .string()
    .min(1, "Ürün kodu zorunludur")
    .max(50, "Ürün kodu en fazla 50 karakter olabilir")
    .trim()
    .regex(
      /^[A-Za-z0-9_-]+$/,
      "<PERSON><PERSON><PERSON><PERSON> kodu sadece harf, rakam, tire ve alt çizgi içerebilir",
    ),

  barcode: z.string().default(""),

  description: z.string().default(""),

  shortDescription: z.string().default(""),

  // Kategori ve vergi
  categoryId: z.string().min(1, "Kategori seçimi zorunludur"),

  taxId: z.string().min(1, "Vergi seçimi zorunludur"),

  // Fiyat bilgileri
  basePrice: z
    .number()
    .positive("Fiyat pozitif bir sayı olmalıdır")
    .min(0.01, "Fiyat en az 0.01 olmalıdır")
    .max(999999.99, "Fiyat çok yüksek"),

  costPrice: z.number().min(0, "Maliyet fiyatı negatif olamaz").default(0),

  profitMargin: z
    .number()
    .min(0, "Kar marjı negatif olamaz")
    .max(100, "Kar marjı %100'den fazla olamaz")
    .default(0),

  // Stok bilgileri
  trackStock: z.boolean().default(false),

  unit: z
    .enum([
      ProductUnit.PIECE,
      ProductUnit.KG,
      ProductUnit.GRAM,
      ProductUnit.LITER,
      ProductUnit.ML,
      ProductUnit.METER,
      ProductUnit.CM,
      ProductUnit.PACKAGE,
      ProductUnit.BOX,
    ])
    .default(ProductUnit.PIECE),

  criticalStock: z.number().min(0, "Kritik stok negatif olamaz").default(0),

  // Durum bilgileri
  available: z.boolean().default(true),

  sellable: z.boolean().default(true),

  active: z.boolean().default(true),

  showInMenu: z.boolean().default(true),

  featured: z.boolean().default(false),

  // Ek bilgiler
  preparationTime: z
    .number()
    .int("Hazırlık süresi tam sayı olmalıdır")
    .min(0, "Hazırlık süresi negatif olamaz")
    .default(0),

  calories: z
    .number()
    .int("Kalori tam sayı olmalıdır")
    .min(0, "Kalori negatif olamaz")
    .default(0),

  allergens: z.array(z.string()).default([]),

  displayOrder: z
    .number()
    .int("Görüntüleme sırası tam sayı olmalıdır")
    .min(0, "Görüntüleme sırası negatif olamaz")
    .default(0),

  // Görsel bilgileri
  imageUrl: z
    .string()
    .default("")
    .refine(
      (val) => {
        // Boş string geçerli
        if (val === "" || val === null || val === undefined) {
          return true;
        }

        // Relative path (/uploads/... ile başlayan) geçerli
        if (val.startsWith("/uploads/")) {
          return true;
        }

        // Tam URL kontrolü
        return z.string().url().safeParse(val).success;
      },
      {
        message: "Geçersiz resim URL'si",
      },
    ),

  images: z
    .array(
      z.string().refine((val) => {
        try {
          new URL(val);
          return true;
        } catch {
          return false;
        }
      }, "Geçersiz resim URL'si"),
    )
    .default([]),

  // Özellik bayrakları
  hasVariants: z.boolean().default(false),

  hasModifiers: z.boolean().default(false),
});

export const productFormSchema = productFormSchemaDefinition;
export type ProductFormData = z.infer<typeof productFormSchema>;

// Güncelleme için kısmi şema
export const updateProductFormSchema = productFormSchema.partial();

export type UpdateProductFormData = z.infer<typeof updateProductFormSchema>;

// Form varsayılan değerleri
export const getDefaultProductFormValues = (): ProductFormData => ({
  name: "",
  code: "",
  barcode: "",
  description: "",
  shortDescription: "",
  categoryId: "",
  taxId: "",
  basePrice: 0,
  costPrice: 0,
  profitMargin: 0,
  trackStock: false,
  unit: ProductUnit.PIECE,
  criticalStock: 0,
  available: true,
  sellable: true,
  active: true,
  showInMenu: true,
  featured: false,
  preparationTime: 0,
  calories: 0,
  allergens: [],
  displayOrder: 0,
  imageUrl: "",
  images: [],
  hasVariants: false,
  hasModifiers: false,
});

// Ürün verilerini form verilerine dönüştürme
export const productToFormData = (product: any): ProductFormData => ({
  name: product.name || "",
  code: product.code || "",
  barcode: product.barcode || "",
  description: product.description || "",
  shortDescription: product.shortDescription || "",
  categoryId: product.categoryId || "",
  taxId: product.taxId || "",
  basePrice:
    typeof product.basePrice === "string"
      ? parseFloat(product.basePrice)
      : product.basePrice || 0,
  costPrice:
    typeof product.costPrice === "string"
      ? parseFloat(product.costPrice)
      : product.costPrice || 0,
  profitMargin:
    typeof product.profitMargin === "string"
      ? parseFloat(product.profitMargin)
      : product.profitMargin || 0,
  trackStock: product.trackStock || false,
  unit: product.unit || ProductUnit.PIECE,
  criticalStock:
    typeof product.criticalStock === "string"
      ? parseFloat(product.criticalStock)
      : product.criticalStock || 0,
  available: product.available !== undefined ? product.available : true,
  sellable: product.sellable !== undefined ? product.sellable : true,
  active: product.active !== undefined ? product.active : true,
  showInMenu: product.showInMenu !== undefined ? product.showInMenu : true,
  featured: product.featured || false,
  preparationTime: product.preparationTime || 0,
  calories: product.calories || 0,
  allergens: product.allergens || [],
  displayOrder: product.displayOrder || 0,
  imageUrl: product.imageUrl || product.image || "",
  images: product.images || [],
  hasVariants: product.hasVariants || false,
  hasModifiers: product.hasModifiers || false,
});
