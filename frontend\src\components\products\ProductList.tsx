import React, { useState, useMemo } from "react";
import { useTranslation } from "react-i18next";
import {
  MagnifyingGlassIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  CheckCircleIcon,
  XCircleIcon,
  Squares2X2Icon,
  ListBulletIcon,
} from "@heroicons/react/24/outline";
import { Button } from "../ui/Button";
import { Input } from "../ui/Input";
import { Select } from "../ui/Select";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/Card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../ui/Table";
import {
  useProducts,
  useCategoriesHierarchy,
  useDeleteProduct,
  useToggleProductStatus,
} from "../../hooks/useProducts";
import type { ProductQueryParams, Product } from "../../types/api";
import { formatCurrency, formatDate, debounce } from "../../lib/utils";
import { cn } from "../../lib/utils";

// Utility function to get full image URL
const getImageUrl = (imageUrl: string | null | undefined): string | null => {
  if (!imageUrl) return null;

  // If it's already a full URL, return as is
  if (imageUrl.startsWith("http://") || imageUrl.startsWith("https://")) {
    return imageUrl;
  }

  // If it's a relative path starting with /uploads/, return as is (Vite proxy will handle it)
  if (imageUrl.startsWith("/uploads/")) {
    return imageUrl;
  }

  return imageUrl;
};

interface ProductListProps {
  onAddProduct: () => void;
  onEditProduct: (product: Product) => void;
}

export const ProductList: React.FC<ProductListProps> = React.memo(
  ({ onAddProduct, onEditProduct }) => {
    const { t } = useTranslation();
    const [searchTerm, setSearchTerm] = useState("");
    const [selectedCategory, setSelectedCategory] = useState<string>("");
    const [statusFilter, setStatusFilter] = useState<string>("");
    const [currentPage, setCurrentPage] = useState(1);
    const [sortBy, setSortBy] = useState<
      "name" | "basePrice" | "createdAt" | "displayOrder"
    >("displayOrder");
    const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
    const [viewMode, setViewMode] = useState<"list" | "grid">("list");

    // Query parameters
    const queryParams: ProductQueryParams = useMemo(
      () => ({
        page: currentPage,
        limit: 20,
        search: searchTerm || undefined,
        categoryId: selectedCategory || undefined,
        active:
          statusFilter === "active"
            ? true
            : statusFilter === "inactive"
              ? false
              : undefined,
        sortBy,
        sortOrder,
      }),
      [
        currentPage,
        searchTerm,
        selectedCategory,
        statusFilter,
        sortBy,
        sortOrder,
      ],
    );

    // Data fetching
    const {
      data: productsResponse,
      isLoading,
      error,
      refetch,
    } = useProducts(queryParams);
    const { data: categoriesResponse } = useCategoriesHierarchy();

    // Mutations
    const deleteProductMutation = useDeleteProduct();
    const toggleStatusMutation = useToggleProductStatus();

    // Debounced search
    const debouncedSearch = useMemo(
      () =>
        debounce((value: string) => {
          setSearchTerm(value);
          setCurrentPage(1);
        }, 300),
      [],
    );

    const handleSearchChange = (value: string) => {
      debouncedSearch(value);
    };

    const handleCategoryChange = (value: string) => {
      setSelectedCategory(value);
      setCurrentPage(1);
    };

    const handleStatusChange = (value: string) => {
      setStatusFilter(value);
      setCurrentPage(1);
    };

    const handleSort = (column: typeof sortBy) => {
      if (sortBy === column) {
        setSortOrder(sortOrder === "asc" ? "desc" : "asc");
      } else {
        setSortBy(column);
        setSortOrder("asc");
      }
      setCurrentPage(1);
    };

    const handleDeleteProduct = async (product: Product) => {
      if (window.confirm(t("products.confirmDelete"))) {
        await deleteProductMutation.mutateAsync(product.id);
      }
    };

    const handleToggleStatus = async (product: Product) => {
      await toggleStatusMutation.mutateAsync({
        id: product.id,
        active: !product.active,
      });
    };

    // Category options for filter
    const categoryOptions = useMemo(() => {
      const options = [{ value: "", label: t("products.allCategories") }];

      if (categoriesResponse?.success) {
        // Always work with an array
        const categories = Array.isArray(categoriesResponse.data)
          ? categoriesResponse.data
          : ((categoriesResponse.data as any)?.categories ?? []);

        const addCategoryOptions = (list: any[] = [], level = 0) => {
          if (!Array.isArray(list)) return;

          list.forEach((category) => {
            const prefix = "  ".repeat(level);
            options.push({
              value: category.id,
              label: `${prefix}${category.name}`,
            });
            if (Array.isArray(category.children) && category.children.length) {
              addCategoryOptions(category.children, level + 1);
            }
          });
        };

        addCategoryOptions(categories);
      }

      return options;
    }, [categoriesResponse, t]);

    const statusOptions = [
      { value: "", label: t("common.status") },
      { value: "active", label: t("common.active") },
      { value: "inactive", label: t("common.inactive") },
    ];

    const products = productsResponse?.success
      ? (productsResponse.data as any)?.products || []
      : [];
    const pagination = productsResponse?.success
      ? productsResponse.data?.pagination
      : null;

    if (error) {
      return (
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-error-500 mb-4">
                {t("common.error")}: {error.message}
              </p>
              <Button onClick={() => refetch()}>{t("common.refresh")}</Button>
            </div>
          </CardContent>
        </Card>
      );
    }

    return (
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>{t("products.productList")}</CardTitle>
            <div className="flex items-center gap-2">
              {/* View Toggle */}
              <div className="flex items-center bg-neutral-100 dark:bg-neutral-800 rounded-lg p-1">
                <button
                  onClick={() => setViewMode("list")}
                  className={cn(
                    "p-2 rounded-md transition-colors",
                    viewMode === "list"
                      ? "bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100 shadow-sm"
                      : "text-neutral-500 dark:text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-300",
                  )}
                  title={t("products.listView")}
                >
                  <ListBulletIcon className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewMode("grid")}
                  className={cn(
                    "p-2 rounded-md transition-colors",
                    viewMode === "grid"
                      ? "bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100 shadow-sm"
                      : "text-neutral-500 dark:text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-300",
                  )}
                  title={t("products.gridView")}
                >
                  <Squares2X2Icon className="w-4 h-4" />
                </button>
              </div>
              <Button
                onClick={onAddProduct}
                className="flex items-center gap-2"
              >
                <PlusIcon className="w-4 h-4" />
                {t("products.addProduct")}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="flex flex-col gap-4 mb-6">
            {/* Search - Full width on mobile */}
            <div className="w-full">
              <Input
                placeholder={t("products.searchProducts")}
                leftIcon={<MagnifyingGlassIcon className="w-4 h-4" />}
                onChange={(e) => handleSearchChange(e.target.value)}
              />
            </div>

            {/* Filters - Stack on mobile, side by side on larger screens */}
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <Select
                  options={categoryOptions}
                  value={selectedCategory}
                  onChange={handleCategoryChange}
                  placeholder={t("products.filterByCategory")}
                />
              </div>
              <div className="w-full sm:w-40">
                <Select
                  options={statusOptions}
                  value={statusFilter}
                  onChange={handleStatusChange}
                  placeholder={t("common.status")}
                />
              </div>
            </div>
          </div>

          {/* List View - Desktop Table */}
          {viewMode === "list" && (
            <div className="hidden lg:block bg-neutral-50 dark:bg-neutral-900 rounded-xl">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-16">{t("common.status")}</TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-neutral-100 dark:hover:bg-neutral-800"
                      onClick={() => handleSort("name")}
                    >
                      <div className="flex items-center gap-1">
                        {t("products.productName")}
                        {sortBy === "name" && (
                          <span className="text-xs">
                            {sortOrder === "asc" ? "↑" : "↓"}
                          </span>
                        )}
                      </div>
                    </TableHead>
                    <TableHead>{t("products.productCode")}</TableHead>
                    <TableHead>{t("products.category")}</TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-neutral-100 dark:hover:bg-neutral-800"
                      onClick={() => handleSort("basePrice")}
                    >
                      <div className="flex items-center gap-1">
                        {t("products.price")}
                        {sortBy === "basePrice" && (
                          <span className="text-xs">
                            {sortOrder === "asc" ? "↑" : "↓"}
                          </span>
                        )}
                      </div>
                    </TableHead>
                    <TableHead>{t("products.stock")}</TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-neutral-100 dark:hover:bg-neutral-800"
                      onClick={() => handleSort("createdAt")}
                    >
                      <div className="flex items-center gap-1">
                        {t("products.createdAt")}
                        {sortBy === "createdAt" && (
                          <span className="text-xs">
                            {sortOrder === "asc" ? "↑" : "↓"}
                          </span>
                        )}
                      </div>
                    </TableHead>
                    <TableHead className="w-32">
                      {t("products.actions")}
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8">
                        {t("common.loading")}
                      </TableCell>
                    </TableRow>
                  ) : products.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8">
                        {t("products.noProductsFound")}
                      </TableCell>
                    </TableRow>
                  ) : (
                    products.map((product: any) => (
                      <TableRow key={product.id}>
                        <TableCell>
                          <button
                            onClick={() => handleToggleStatus(product)}
                            disabled={toggleStatusMutation.isPending}
                            className={cn(
                              "p-1 rounded-full transition-colors",
                              product.active
                                ? "text-success-500 hover:bg-success-100 dark:hover:bg-success-900"
                                : "text-neutral-400 hover:bg-neutral-100 dark:hover:bg-neutral-800",
                            )}
                          >
                            {product.active ? (
                              <CheckCircleIcon className="w-5 h-5" />
                            ) : (
                              <XCircleIcon className="w-5 h-5" />
                            )}
                          </button>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            {(() => {
                              const imageUrl = getImageUrl(
                                product.imageUrl || product.image,
                              );
                              return imageUrl ? (
                                <img
                                  src={imageUrl}
                                  alt={product.name}
                                  className="w-10 h-10 rounded-md object-cover bg-neutral-100 dark:bg-neutral-800"
                                  onError={(e) => {
                                    // Resim yüklenemezse placeholder göster
                                    e.currentTarget.style.display = "none";
                                    const placeholder = e.currentTarget
                                      .nextElementSibling as HTMLElement;
                                    if (placeholder)
                                      placeholder.style.display = "flex";
                                  }}
                                />
                              ) : null;
                            })()}
                            {/* Placeholder for missing images */}
                            <div
                              className="w-10 h-10 rounded-md bg-neutral-200 dark:bg-neutral-700 flex items-center justify-center text-neutral-400 dark:text-neutral-500 text-xs"
                              style={{
                                display: getImageUrl(
                                  product.imageUrl || product.image,
                                )
                                  ? "none"
                                  : "flex",
                              }}
                            >
                              📷
                            </div>
                            <div>
                              <div className="font-medium">{product.name}</div>
                              {product.shortDescription && (
                                <div className="text-sm text-neutral-500 dark:text-neutral-400">
                                  {product.shortDescription}
                                </div>
                              )}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <code className="text-sm bg-neutral-100 dark:bg-neutral-800 px-2 py-1 rounded">
                            {product.code}
                          </code>
                        </TableCell>
                        <TableCell>
                          {product.category && (
                            <span
                              className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                              style={{
                                backgroundColor: product.category.color
                                  ? `${product.category.color}20`
                                  : undefined,
                                color: product.category.color || undefined,
                              }}
                            >
                              {product.category.name}
                            </span>
                          )}
                        </TableCell>
                        <TableCell className="font-medium">
                          {formatCurrency(product.basePrice)}
                        </TableCell>
                        <TableCell>
                          {product.trackStock ? (
                            <span className="text-sm">
                              {/* Stock bilgisi backend'den gelecek */}-
                            </span>
                          ) : (
                            <span className="text-sm text-neutral-500 dark:text-neutral-400">
                              {t("products.noTracking")}
                            </span>
                          )}
                        </TableCell>
                        <TableCell className="text-sm text-neutral-500 dark:text-neutral-400">
                          {formatDate(product.createdAt)}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => onEditProduct(product)}
                              className="h-8 w-8"
                            >
                              <PencilIcon className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleDeleteProduct(product)}
                              disabled={deleteProductMutation.isPending}
                              className="h-8 w-8 text-error-500 hover:text-error-600 hover:bg-error-100 dark:hover:bg-error-900"
                            >
                              <TrashIcon className="w-4 h-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          )}

          {/* Mobile Card View - Visible only on mobile and in list mode */}
          {viewMode === "list" && (
            <div className="lg:hidden space-y-4">
              {isLoading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 bg-gradient-to-r from-primary-500 to-transparent mx-auto mb-4"></div>
                  <p className="text-neutral-500 dark:text-neutral-400">
                    {t("common.loading")}
                  </p>
                </div>
              ) : products.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-neutral-500 dark:text-neutral-400">
                    {t("products.noProductsFound")}
                  </p>
                </div>
              ) : (
                products.map((product: any) => (
                  <Card key={product.id} className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center space-x-3 flex-1 min-w-0">
                        {(() => {
                          const imageUrl = getImageUrl(
                            product.imageUrl || product.image,
                          );
                          return imageUrl ? (
                            <img
                              src={imageUrl}
                              alt={product.name}
                              className="w-12 h-12 rounded-md object-cover flex-shrink-0 bg-neutral-100 dark:bg-neutral-800"
                              onError={(e) => {
                                // Resim yüklenemezse placeholder göster
                                e.currentTarget.style.display = "none";
                                const placeholder = e.currentTarget
                                  .nextElementSibling as HTMLElement;
                                if (placeholder)
                                  placeholder.style.display = "flex";
                              }}
                            />
                          ) : null;
                        })()}
                        {/* Placeholder for missing images */}
                        <div
                          className="w-12 h-12 rounded-md bg-neutral-200 dark:bg-neutral-700 flex items-center justify-center text-neutral-400 dark:text-neutral-500 flex-shrink-0"
                          style={{
                            display: getImageUrl(
                              product.imageUrl || product.image,
                            )
                              ? "none"
                              : "flex",
                          }}
                        >
                          📷
                        </div>
                        <div className="min-w-0 flex-1">
                          <h3 className="font-medium text-neutral-black dark:text-neutral-white truncate">
                            {product.name}
                          </h3>
                          <p className="text-sm text-neutral-500 dark:text-neutral-400 truncate">
                            {product.code}
                          </p>
                        </div>
                      </div>
                      <button
                        onClick={() => handleToggleStatus(product)}
                        disabled={toggleStatusMutation.isPending}
                        className={cn(
                          "p-1 rounded-full transition-colors flex-shrink-0",
                          product.active
                            ? "text-success-500 hover:bg-success-100 dark:hover:bg-success-900"
                            : "text-neutral-400 hover:bg-neutral-100 dark:hover:bg-neutral-800",
                        )}
                      >
                        {product.active ? (
                          <CheckCircleIcon className="w-5 h-5" />
                        ) : (
                          <XCircleIcon className="w-5 h-5" />
                        )}
                      </button>
                    </div>

                    <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                      <div>
                        <span className="text-neutral-500 dark:text-neutral-400 block">
                          {t("products.category")}
                        </span>
                        {product.category && (
                          <span
                            className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium mt-1"
                            style={{
                              backgroundColor: product.category.color
                                ? `${product.category.color}20`
                                : undefined,
                              color: product.category.color || undefined,
                            }}
                          >
                            {product.category.name}
                          </span>
                        )}
                      </div>
                      <div>
                        <span className="text-neutral-500 dark:text-neutral-400 block">
                          {t("products.price")}
                        </span>
                        <span className="font-medium text-neutral-black dark:text-neutral-white">
                          {formatCurrency(product.basePrice)}
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-xs text-neutral-500 dark:text-neutral-400">
                        {formatDate(product.createdAt)}
                      </span>
                      <div className="flex items-center space-x-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onEditProduct(product)}
                          className="h-8 px-2"
                        >
                          <PencilIcon className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteProduct(product)}
                          disabled={deleteProductMutation.isPending}
                          className="h-8 px-2 text-error-500 hover:text-error-600 hover:bg-error-100 dark:hover:bg-error-900"
                        >
                          <TrashIcon className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </Card>
                ))
              )}
            </div>
          )}

          {/* Grid View */}
          {viewMode === "grid" && (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {isLoading ? (
                Array.from({ length: 8 }).map((_, index) => (
                  <Card key={index} className="p-4 animate-pulse">
                    <div className="aspect-square bg-neutral-200 dark:bg-neutral-700 rounded-lg mb-3"></div>
                    <div className="h-4 bg-neutral-200 dark:bg-neutral-700 rounded mb-2"></div>
                    <div className="h-3 bg-neutral-200 dark:bg-neutral-700 rounded w-2/3"></div>
                  </Card>
                ))
              ) : products.length === 0 ? (
                <div className="col-span-full text-center py-12">
                  <p className="text-neutral-500 dark:text-neutral-400">
                    {t("products.noProductsFound")}
                  </p>
                </div>
              ) : (
                products.map((product: any) => (
                  <Card
                    key={product.id}
                    className="group hover:shadow-lg transition-shadow duration-200"
                  >
                    <div className="p-4">
                      {/* Product Image */}
                      <div className="aspect-square mb-3 relative overflow-hidden rounded-lg bg-neutral-100 dark:bg-neutral-800">
                        {(() => {
                          const imageUrl = getImageUrl(
                            product.imageUrl || product.image,
                          );
                          return imageUrl ? (
                            <img
                              src={imageUrl}
                              alt={product.name}
                              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                              onError={(e) => {
                                // Resim yüklenemezse placeholder göster
                                e.currentTarget.style.display = "none";
                                const placeholder = e.currentTarget
                                  .nextElementSibling as HTMLElement;
                                if (placeholder)
                                  placeholder.style.display = "flex";
                              }}
                            />
                          ) : null;
                        })()}
                        {/* Placeholder for missing images */}
                        <div
                          className="absolute inset-0 flex items-center justify-center text-neutral-400 dark:text-neutral-500 text-4xl"
                          style={{
                            display: getImageUrl(
                              product.imageUrl || product.image,
                            )
                              ? "none"
                              : "flex",
                          }}
                        >
                          📷
                        </div>

                        {/* Status Badge */}
                        <div className="absolute top-2 left-2">
                          <button
                            onClick={() => handleToggleStatus(product)}
                            disabled={toggleStatusMutation.isPending}
                            className={cn(
                              "p-1 rounded-full transition-colors backdrop-blur-sm",
                              product.active
                                ? "bg-success-500/20 text-success-600 hover:bg-success-500/30"
                                : "bg-neutral-500/20 text-neutral-400 hover:bg-neutral-500/30",
                            )}
                          >
                            {product.active ? (
                              <CheckCircleIcon className="w-4 h-4" />
                            ) : (
                              <XCircleIcon className="w-4 h-4" />
                            )}
                          </button>
                        </div>

                        {/* Action Buttons */}
                        <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                          <div className="flex gap-1">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => onEditProduct(product)}
                              className="h-8 w-8 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm hover:bg-white dark:hover:bg-neutral-800"
                            >
                              <PencilIcon className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleDeleteProduct(product)}
                              disabled={deleteProductMutation.isPending}
                              className="h-8 w-8 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm hover:bg-white dark:hover:bg-neutral-800 text-error-500 hover:text-error-600"
                            >
                              <TrashIcon className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      </div>

                      {/* Product Info */}
                      <div className="space-y-2">
                        <h3 className="font-medium text-neutral-black dark:text-neutral-white truncate">
                          {product.name}
                        </h3>

                        <div className="flex items-center justify-between">
                          <code className="text-xs bg-neutral-100 dark:bg-neutral-800 px-2 py-1 rounded">
                            {product.code}
                          </code>
                          <span className="font-semibold text-primary-600 dark:text-primary-400">
                            {formatCurrency(product.basePrice)}
                          </span>
                        </div>

                        {product.category && (
                          <div className="flex items-center gap-2">
                            <span
                              className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                              style={{
                                backgroundColor: product.category.color
                                  ? `${product.category.color}20`
                                  : undefined,
                                color: product.category.color || undefined,
                              }}
                            >
                              {product.category.name}
                            </span>
                          </div>
                        )}

                        <div className="text-xs text-neutral-500 dark:text-neutral-400">
                          {formatDate(product.createdAt)}
                        </div>
                      </div>
                    </div>
                  </Card>
                ))
              )}
            </div>
          )}

          {/* Pagination */}
          {pagination && pagination.totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-neutral-500 dark:text-neutral-400">
                {t("common.showing")}{" "}
                {(pagination.page - 1) * pagination.limit + 1} -{" "}
                {Math.min(pagination.page * pagination.limit, pagination.total)}{" "}
                {t("common.of")} {pagination.total}
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(pagination.page - 1)}
                  disabled={!pagination.hasPrevPage}
                >
                  {t("common.previous")}
                </Button>
                <span className="text-sm">
                  {pagination.page} / {pagination.totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(pagination.page + 1)}
                  disabled={!pagination.hasNextPage}
                >
                  {t("common.next")}
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  },
);
