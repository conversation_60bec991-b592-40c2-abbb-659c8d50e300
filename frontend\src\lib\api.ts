// API client configuration
const API_BASE_URL =
  import.meta.env.VITE_API_URL || "http://localhost:3000/api";

// Auth store import - dinamik import kullanarak circular dependency'y<PERSON><PERSON>
let authStore: any = null;
const getAuthStore = async () => {
  if (!authStore) {
    const { useAuthStore } = await import("./store");
    authStore = useAuthStore;
  }
  return authStore;
};

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

class ApiClient {
  private baseURL: string;
  private token: string | null = null;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
    this.token = localStorage.getItem("auth_token");
  }

  setToken(token: string | null) {
    this.token = token;
    if (token) {
      localStorage.setItem("auth_token", token);
    } else {
      localStorage.removeItem("auth_token");
    }
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;

    const headers: Record<string, string> = {};

    // Mevcut header'ları kopyala
    if (options.headers) {
      if (Array.isArray(options.headers)) {
        options.headers.forEach(([key, value]) => {
          headers[key] = value;
        });
      } else if (options.headers instanceof Headers) {
        options.headers.forEach((value, key) => {
          headers[key] = value;
        });
      } else {
        Object.assign(headers, options.headers);
      }
    }

    // Sadece body varsa ve FormData değilse Content-Type ekle
    if (options.body && !(options.body instanceof FormData)) {
      headers["Content-Type"] = "application/json";
    }

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      const data = await response.json();

      if (!response.ok) {
        // 401 hatası durumunda otomatik logout yap
        if (response.status === 401) {
          const store = await getAuthStore();
          store.getState().logout();

          // Login sayfasına yönlendir
          if (typeof window !== "undefined") {
            window.location.href = "/login";
          }
        }

        throw new Error(data.message || "API request failed");
      }

      return data;
    } catch (error) {
      console.error("API Error:", error);

      // Network hatası veya diğer hatalar için de 401 kontrolü yap
      if (error instanceof TypeError && error.message.includes("fetch")) {
        // Network hatası - token geçersiz olabilir
        console.warn("Network error - possible token issue");
      }

      throw error;
    }
  }

  // Auth endpoints
  async login(username: string, password: string) {
    return this.request<{ user: any; token: string }>("/auth/login", {
      method: "POST",
      body: JSON.stringify({ username, password }),
    });
  }

  async logout() {
    return this.request("/auth/logout", {
      method: "POST",
    });
  }

  async getMe() {
    return this.request<any>("/auth/me");
  }

  // Generic CRUD methods
  async get<T>(endpoint: string) {
    return this.request<T>(endpoint);
  }

  async post<T>(endpoint: string, data: any, options?: RequestInit) {
    const body = data instanceof FormData ? data : JSON.stringify(data);
    return this.request<T>(endpoint, {
      method: "POST",
      body,
      ...options,
    });
  }

  async put<T>(endpoint: string, data: any, options?: RequestInit) {
    const body = data instanceof FormData ? data : JSON.stringify(data);
    return this.request<T>(endpoint, {
      method: "PUT",
      body,
      ...options,
    });
  }

  async patch<T>(endpoint: string, data: any, options?: RequestInit) {
    const body = data instanceof FormData ? data : JSON.stringify(data);
    return this.request<T>(endpoint, {
      method: "PATCH",
      body,
      ...options,
    });
  }

  async delete<T>(endpoint: string, options?: RequestInit) {
    return this.request<T>(endpoint, {
      method: "DELETE",
      ...options,
    });
  }
}

export const apiClient = new ApiClient(API_BASE_URL);
