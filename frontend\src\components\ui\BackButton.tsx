import React from "react";
import { useNavigate } from "react-router-dom";
import { ArrowLeftIcon } from "@heroicons/react/24/outline";
import { Button } from "./Button";

interface BackButtonProps {
  to?: string;
  onClick?: () => void;
  className?: string;
  size?: "sm" | "default" | "lg";
}

export const BackButton: React.FC<BackButtonProps> = ({
  to = "/dashboard",
  onClick,
  className,
  size = "default",
}) => {
  const navigate = useNavigate();

  const handleClick = () => {
    if (onClick) {
      onClick();
    } else {
      navigate(to);
    }
  };

  return (
    <Button
      variant="ghost"
      size={size}
      onClick={handleClick}
      className={`flex items-center gap-2 ${className}`}
    >
      <ArrowLeftIcon className="w-5 h-5" />
    </Button>
  );
};
