import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { ThemeToggle } from "../components/ThemeToggle";
import { LanguageSelector } from "../components/LanguageSelector";
import { apiClient } from "../lib/api";
import { useAuthStore } from "../lib/store";
import { ArrowPathIcon } from "@heroicons/react/24/outline";

export function Login() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { login } = useAuthStore();
  const [username, setUsername] = useState("admin");
  const [password, setPassword] = useState("admin123");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    try {
      // Gerçek API çağrısı
      const response = await apiClient.login(username, password);

      if (response.success && response.data) {
        // Zustand store'a kullanıcı bilgilerini kaydet (token da otomatik set edilir)
        login(response.data.user, response.data.token);

        // Dashboard'a yönlendir
        navigate("/dashboard");
      } else {
        setError(response.message || t("auth.loginFailed", "Giriş başarısız"));
      }
    } catch (err: any) {
      console.error("Login error:", err);
      setError(
        err.message || t("auth.loginError", "Giriş yapılırken bir hata oluştu"),
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-neutral-100 dark:bg-neutral-800 flex items-center justify-center transition-colors">
      <div className="absolute top-4 right-4 flex items-center space-x-4">
        <ThemeToggle />
        <LanguageSelector />
      </div>

      <div className="max-w-md w-full space-y-8 p-8">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-neutral-black dark:text-neutral-white mb-2">
            {t("app.name", "Bitepoint POS")}
          </h1>
          <p className="text-neutral-600 dark:text-neutral-400">
            {t(
              "auth.welcomeMessage",
              "Restoran yönetim sisteminize hoş geldiniz",
            )}
          </p>
        </div>

        <div className="bg-white dark:bg-neutral-black rounded-lg shadow-xl p-8 transition-colors">
          <form onSubmit={handleSubmit} className="space-y-6">
            {error && (
              <div className="bg-error-500/10 dark:bg-error-500/20 border border-error-500/30 dark:border-error-500/40 text-error-500 dark:text-error-500 px-4 py-3 rounded">
                {error}
              </div>
            )}

            <div>
              <label
                htmlFor="username"
                className="block text-sm font-medium text-neutral-600 dark:text-neutral-300 mb-2"
              >
                {t("auth.username", "Kullanıcı Adı")}
              </label>
              <input
                id="username"
                type="text"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-md shadow-sm bg-neutral-100 dark:bg-neutral-800 text-neutral-black dark:text-neutral-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                placeholder="admin"
                autoComplete="username"
                required
              />
            </div>

            <div>
              <label
                htmlFor="password"
                className="block text-sm font-medium text-neutral-600 dark:text-neutral-300 mb-2"
              >
                {t("auth.password", "Şifre")}
              </label>
              <input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-md shadow-sm bg-neutral-100 dark:bg-neutral-800 text-neutral-black dark:text-neutral-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                placeholder="admin123"
                autoComplete="current-password"
                required
              />
            </div>

            <button
              type="submit"
              disabled={loading}
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-500 hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {loading ? (
                <div className="flex items-center">
                  <ArrowPathIcon className="animate-spin h-4 w-4 text-white mr-2" />
                  {t("common.loading", "Yükleniyor...")}
                </div>
              ) : (
                t("auth.login", "Giriş Yap")
              )}
            </button>
          </form>

          <div className="mt-6 text-center">
            <p className="text-sm text-neutral-600 dark:text-neutral-400">
              {t("auth.testCredentials", "Test için")}:{" "}
              <strong>admin / admin123</strong>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
