import { FastifyInstance, FastifyRequest, FastifyReply } from "fastify";
import fp from "fastify-plugin";
import { prisma } from "../index";

// JWT payload tipi
interface JWTPayload {
  id: string;
  role: string;
  branchId: string | null;
  companyId: string;
}

declare module "@fastify/jwt" {
  interface FastifyJWT {
    payload: JWTPayload;
    user: JWTPayload;
  }
}

async function authPlugin(fastify: FastifyInstance) {
  fastify.decorate(
    "authenticate",
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const token = request.headers.authorization?.replace("Bearer ", "");

        if (!token) {
          return reply.status(401).send({
            success: false,
            error: "Unauthorized",
            message: "Token bulunamadı",
          });
        }

        // Token'ı doğrula
        const decoded = fastify.jwt.verify(token) as JWTPayload;

        // Oturumu kontrol et
        const session = await prisma.session.findFirst({
          where: {
            token,
            userId: decoded.id,
            endedAt: null,
          },
          include: {
            user: {
              include: {
                branch: true,
                company: true,
              },
            },
          },
        });

        if (!session || !session.user.active || session.user.deletedAt) {
          return reply.status(401).send({
            success: false,
            error: "Unauthorized",
            message: "Geçersiz oturum",
          });
        }

        // Son aktivite zamanını güncelle
        await prisma.session.update({
          where: { id: session.id },
          data: { lastActivityAt: new Date() },
        });

        // Kullanıcı bilgilerini request'e ekle
        request.user = {
          id: session.user.id,
          role: session.user.role,
          branchId: session.user.branchId,
          companyId: session.user.companyId,
        };
      } catch {
        return reply.status(401).send({
          success: false,
          error: "Unauthorized",
          message: "Geçersiz token",
        });
      }
    },
  );

  // Rol bazlı yetkilendirme
  fastify.decorate("authorize", (roles: string[]) => {
    return async (request: FastifyRequest, reply: FastifyReply) => {
      if (!request.user) {
        return reply.status(401).send({
          success: false,
          error: "Unauthorized",
          message: "Kimlik doğrulaması gerekli",
        });
      }

      if (!roles.includes(request.user.role)) {
        return reply.status(403).send({
          success: false,
          error: "Forbidden",
          message: "Bu işlem için yetkiniz yok",
        });
      }
    };
  });
}

export default fp(authPlugin);
