import { useEffect } from "react";
import { useAuthStore } from "../lib/store";
import { apiClient } from "../lib/api";

export const useAuth = () => {
  const { isAuthenticated, token, logout } = useAuthStore();

  // Token geçerliliğini kontrol et
  useEffect(() => {
    const checkTokenValidity = async () => {
      if (isAuthenticated && token) {
        try {
          // /auth/me endpoint'ini çağırarak token'ın geçerliliğini kontrol et
          await apiClient.getMe();
        } catch (error) {
          console.warn("Token geçersiz, kullanıcı çıkış yapılıyor:", error);
          logout();
        }
      }
    };

    // Sayfa yüklendiğinde token'ı kontrol et
    if (isAuthenticated && token) {
      checkTokenValidity();
    }

    // Periyodik olarak token'ı kontrol et (her 5 dakikada bir)
    const interval = setInterval(
      () => {
        if (isAuthenticated && token) {
          checkTokenValidity();
        }
      },
      5 * 60 * 1000,
    ); // 5 dakika

    return () => clearInterval(interval);
  }, [isAuthenticated, token, logout]);

  return {
    isAuthenticated,
    token,
    logout,
  };
};
