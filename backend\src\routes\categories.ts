import { FastifyInstance, FastifyRequest, FastifyReply } from "fastify";
import { prisma } from "../index";
import { z } from "zod";

// JWT payload tipi
interface JWTPayload {
  id: string;
  role: string;
  branchId: string | null;
  companyId: string;
}

// Fastify instance'ını auth plugin decorators ile genişlet
declare module "fastify" {
  interface FastifyInstance {
    authenticate: (
      request: FastifyRequest,
      reply: FastifyReply,
    ) => Promise<void>;
    authorize: (
      roles: string[],
    ) => (request: FastifyRequest, reply: FastifyReply) => Promise<void>;
  }
}

// Validation şemaları
const createCategorySchema = z.object({
  parentId: z.string().cuid("Geçersiz üst kategori ID").optional(),
  name: z
    .string()
    .min(2, "Kategori adı en az 2 karakter olmalıdır")
    .max(100, "Kategori adı en fazla 100 karakter olabilir"),
  description: z.string().optional(),
  image: z.string().url("Geçersiz resim URL'si").optional(),
  color: z
    .string()
    .regex(/^#[0-9A-Fa-f]{6}$/, "Geçersiz hex renk formatı (#RRGGBB)")
    .optional(),
  icon: z.string().optional(),
  showInKitchen: z.boolean().default(true),
  preparationTime: z
    .number()
    .int()
    .min(0, "Hazırlık süresi negatif olamaz")
    .optional(),
  displayOrder: z
    .number()
    .int()
    .min(0, "Görüntüleme sırası negatif olamaz")
    .default(0),
  active: z.boolean().default(true),
  showInMenu: z.boolean().default(true),
});

const updateCategorySchema = createCategorySchema.partial();

const querySchema = z.object({
  page: z
    .string()
    .transform((val) => parseInt(val))
    .pipe(z.number().int().min(1))
    .default("1"),
  limit: z
    .string()
    .transform((val) => parseInt(val))
    .pipe(z.number().int().min(1).max(100))
    .default("20"),
  search: z.string().optional(),
  parentId: z.string().cuid().optional(),
  active: z
    .string()
    .transform((val) => val === "true")
    .pipe(z.boolean())
    .optional(),
  showInMenu: z
    .string()
    .transform((val) => val === "true")
    .pipe(z.boolean())
    .optional(),
  showInKitchen: z
    .string()
    .transform((val) => val === "true")
    .pipe(z.boolean())
    .optional(),
  sortBy: z.enum(["name", "displayOrder", "createdAt"]).default("displayOrder"),
  sortOrder: z.enum(["asc", "desc"]).default("asc"),
  includeHierarchy: z
    .string()
    .transform((val) => val === "true")
    .pipe(z.boolean())
    .default("false"),
});

export default async function (fastify: FastifyInstance) {
  // Tüm kategorileri listele (hiyerarşik yapı ile)
  fastify.get(
    "/",
    {
      onRequest: [fastify.authenticate],
    },
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const user = request.user as JWTPayload;
        const query = querySchema.parse(request.query);

        // Filtreleme koşulları
        const where: any = {
          companyId: user.companyId,
          deletedAt: null,
        };

        if (query.search) {
          where.OR = [
            { name: { contains: query.search, mode: "insensitive" } },
            { description: { contains: query.search, mode: "insensitive" } },
          ];
        }

        if (query.parentId !== undefined) {
          where.parentId = query.parentId;
        }

        if (query.active !== undefined) {
          where.active = query.active;
        }

        if (query.showInMenu !== undefined) {
          where.showInMenu = query.showInMenu;
        }

        if (query.showInKitchen !== undefined) {
          where.showInKitchen = query.showInKitchen;
        }

        // Hiyerarşik yapı istenmişse, sadece ana kategorileri getir
        if (query.includeHierarchy) {
          where.parentId = null;
        }

        // Sıralama
        const orderBy: any = {};
        orderBy[query.sortBy] = query.sortOrder;

        // Sayfalama hesaplamaları
        const skip = (query.page - 1) * query.limit;

        // Toplam kayıt sayısı
        const total = await prisma.category.count({ where });

        // Include objesi
        const include: any = {
          parent: {
            select: {
              id: true,
              name: true,
              color: true,
              icon: true,
            },
          },
          _count: {
            select: {
              children: true,
              products: true,
            },
          },
        };

        // Hiyerarşik yapı istenmişse alt kategorileri de dahil et
        if (query.includeHierarchy) {
          include.children = {
            where: { deletedAt: null },
            orderBy: { displayOrder: "asc" },
            include: {
              _count: {
                select: {
                  products: true,
                },
              },
            },
          };
        }

        // Kategorileri getir
        const categories = await prisma.category.findMany({
          where,
          include,
          orderBy,
          skip: query.includeHierarchy ? undefined : skip,
          take: query.includeHierarchy ? undefined : query.limit,
        });

        // Sayfalama bilgileri
        const totalPages = Math.ceil(total / query.limit);
        const hasNextPage = query.page < totalPages;
        const hasPrevPage = query.page > 1;

        return reply.send({
          success: true,
          data: {
            categories,
            pagination: query.includeHierarchy
              ? null
              : {
                  page: query.page,
                  limit: query.limit,
                  total,
                  totalPages,
                  hasNextPage,
                  hasPrevPage,
                },
          },
        });
      } catch (error) {
        fastify.log.error(error);
        return reply.status(400).send({
          success: false,
          error: "Bad Request",
          message:
            error instanceof z.ZodError
              ? "Geçersiz sorgu parametreleri"
              : "Kategoriler listelenirken bir hata oluştu",
        });
      }
    },
  );

  // Tek kategori detayı
  fastify.route({
    method: "GET",
    url: "/:id",
    onRequest: [fastify.authenticate],
    handler: async (
      request: FastifyRequest<{ Params: { id: string } }>,
      reply: FastifyReply,
    ) => {
      try {
        const user = request.user as JWTPayload;
        const { id } = request.params;

        if (!id || typeof id !== "string") {
          return reply.status(400).send({
            success: false,
            error: "Bad Request",
            message: "Geçersiz kategori ID",
          });
        }

        const category = await prisma.category.findFirst({
          where: {
            id,
            companyId: user.companyId,
            deletedAt: null,
          },
          include: {
            parent: {
              select: {
                id: true,
                name: true,
                color: true,
                icon: true,
              },
            },
            children: {
              where: { deletedAt: null },
              orderBy: { displayOrder: "asc" },
              include: {
                _count: {
                  select: {
                    products: true,
                  },
                },
              },
            },
            products: {
              where: { deletedAt: null },
              select: {
                id: true,
                name: true,
                code: true,
                basePrice: true,
                active: true,
                available: true,
              },
              orderBy: { displayOrder: "asc" },
            },
            _count: {
              select: {
                children: true,
                products: true,
              },
            },
          },
        });

        if (!category) {
          return reply.status(404).send({
            success: false,
            error: "Not Found",
            message: "Kategori bulunamadı",
          });
        }

        return reply.send({
          success: true,
          data: category,
        });
      } catch (error) {
        fastify.log.error(error);
        return reply.status(500).send({
          success: false,
          error: "Internal Server Error",
          message: "Kategori detayı alınırken bir hata oluştu",
        });
      }
    },
  });

  // Yeni kategori oluştur
  fastify.post(
    "/",
    {
      onRequest: [
        fastify.authenticate,
        fastify.authorize(["SUPER_ADMIN", "ADMIN", "BRANCH_MANAGER"]),
      ],
    },
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const user = request.user as JWTPayload;
        const categoryData = createCategorySchema.parse(request.body);

        // Üst kategori kontrolü (eğer varsa)
        if (categoryData.parentId) {
          const parentCategory = await prisma.category.findFirst({
            where: {
              id: categoryData.parentId,
              companyId: user.companyId,
              deletedAt: null,
            },
          });

          if (!parentCategory) {
            return reply.status(400).send({
              success: false,
              error: "Bad Request",
              message: "Geçersiz üst kategori",
            });
          }

          // Döngüsel referans kontrolü (parent'ın parent'ı olup olmadığını kontrol et)
          let currentParent = parentCategory;
          while (currentParent.parentId) {
            const nextParent = await prisma.category.findFirst({
              where: {
                id: currentParent.parentId,
                companyId: user.companyId,
                deletedAt: null,
              },
            });

            if (!nextParent) break;
            currentParent = nextParent;
          }
        }

        // Kategori adı benzersizlik kontrolü (aynı parent altında)
        const existingCategory = await prisma.category.findFirst({
          where: {
            name: categoryData.name,
            parentId: categoryData.parentId || null,
            companyId: user.companyId,
            deletedAt: null,
          },
        });

        if (existingCategory) {
          return reply.status(400).send({
            success: false,
            error: "Bad Request",
            message: "Bu kategori adı zaten kullanılıyor",
          });
        }

        // Kategoriyi oluştur
        const category = await prisma.category.create({
          data: {
            ...categoryData,
            companyId: user.companyId,
          },
          include: {
            parent: {
              select: {
                id: true,
                name: true,
                color: true,
                icon: true,
              },
            },
            _count: {
              select: {
                children: true,
                products: true,
              },
            },
          },
        });

        return reply.status(201).send({
          success: true,
          data: category,
          message: "Kategori başarıyla oluşturuldu",
        });
      } catch (error) {
        fastify.log.error(error);
        return reply.status(400).send({
          success: false,
          error: "Bad Request",
          message:
            error instanceof z.ZodError
              ? "Geçersiz kategori bilgileri"
              : "Kategori oluşturulurken bir hata oluştu",
        });
      }
    },
  );

  // Kategori güncelle
  fastify.route({
    method: "PUT",
    url: "/:id",
    onRequest: [
      fastify.authenticate,
      fastify.authorize(["SUPER_ADMIN", "ADMIN", "BRANCH_MANAGER"]),
    ],
    handler: async (
      request: FastifyRequest<{ Params: { id: string } }>,
      reply: FastifyReply,
    ) => {
      try {
        const user = request.user as JWTPayload;
        const { id } = request.params;
        const updateData = updateCategorySchema.parse(request.body);

        if (!id || typeof id !== "string") {
          return reply.status(400).send({
            success: false,
            error: "Bad Request",
            message: "Geçersiz kategori ID",
          });
        }

        // Mevcut kategoriyi kontrol et
        const existingCategory = await prisma.category.findFirst({
          where: {
            id,
            companyId: user.companyId,
            deletedAt: null,
          },
        });

        if (!existingCategory) {
          return reply.status(404).send({
            success: false,
            error: "Not Found",
            message: "Kategori bulunamadı",
          });
        }

        // Üst kategori kontrolü (eğer güncellenmişse)
        if (updateData.parentId !== undefined) {
          if (updateData.parentId) {
            // Kendisini parent olarak seçemez
            if (updateData.parentId === id) {
              return reply.status(400).send({
                success: false,
                error: "Bad Request",
                message: "Kategori kendisinin üst kategorisi olamaz",
              });
            }

            const parentCategory = await prisma.category.findFirst({
              where: {
                id: updateData.parentId,
                companyId: user.companyId,
                deletedAt: null,
              },
            });

            if (!parentCategory) {
              return reply.status(400).send({
                success: false,
                error: "Bad Request",
                message: "Geçersiz üst kategori",
              });
            }

            // Döngüsel referans kontrolü
            let currentParent = parentCategory;
            while (currentParent.parentId) {
              if (currentParent.parentId === id) {
                return reply.status(400).send({
                  success: false,
                  error: "Bad Request",
                  message: "Döngüsel kategori ilişkisi oluşturulamaz",
                });
              }

              const nextParent = await prisma.category.findFirst({
                where: {
                  id: currentParent.parentId,
                  companyId: user.companyId,
                  deletedAt: null,
                },
              });

              if (!nextParent) break;
              currentParent = nextParent;
            }
          }
        }

        // Kategori adı benzersizlik kontrolü (eğer güncellenmişse)
        if (updateData.name && updateData.name !== existingCategory.name) {
          const nameExists = await prisma.category.findFirst({
            where: {
              name: updateData.name,
              parentId:
                updateData.parentId !== undefined
                  ? updateData.parentId
                  : existingCategory.parentId,
              companyId: user.companyId,
              deletedAt: null,
              NOT: { id },
            },
          });

          if (nameExists) {
            return reply.status(400).send({
              success: false,
              error: "Bad Request",
              message: "Bu kategori adı zaten kullanılıyor",
            });
          }
        }

        // Kategoriyi güncelle
        const updatedCategory = await prisma.category.update({
          where: { id },
          data: {
            ...updateData,
            version: { increment: 1 }, // Optimistic locking
          },
          include: {
            parent: {
              select: {
                id: true,
                name: true,
                color: true,
                icon: true,
              },
            },
            _count: {
              select: {
                children: true,
                products: true,
              },
            },
          },
        });

        return reply.send({
          success: true,
          data: updatedCategory,
          message: "Kategori başarıyla güncellendi",
        });
      } catch (error) {
        fastify.log.error(error);
        return reply.status(400).send({
          success: false,
          error: "Bad Request",
          message:
            error instanceof z.ZodError
              ? "Geçersiz kategori bilgileri"
              : "Kategori güncellenirken bir hata oluştu",
        });
      }
    },
  });

  // Kategori sil (soft delete)
  fastify.route({
    method: "DELETE",
    url: "/:id",
    onRequest: [
      fastify.authenticate,
      fastify.authorize(["SUPER_ADMIN", "ADMIN", "BRANCH_MANAGER"]),
    ],
    handler: async (
      request: FastifyRequest<{ Params: { id: string } }>,
      reply: FastifyReply,
    ) => {
      try {
        const user = request.user as JWTPayload;
        const { id } = request.params;

        if (!id || typeof id !== "string") {
          return reply.status(400).send({
            success: false,
            error: "Bad Request",
            message: "Geçersiz kategori ID",
          });
        }

        // Mevcut kategoriyi kontrol et
        const existingCategory = await prisma.category.findFirst({
          where: {
            id,
            companyId: user.companyId,
            deletedAt: null,
          },
        });

        if (!existingCategory) {
          return reply.status(404).send({
            success: false,
            error: "Not Found",
            message: "Kategori bulunamadı",
          });
        }

        // Alt kategorileri kontrol et
        const childCategories = await prisma.category.findMany({
          where: {
            parentId: id,
            companyId: user.companyId,
            deletedAt: null,
          },
        });

        if (childCategories.length > 0) {
          return reply.status(400).send({
            success: false,
            error: "Bad Request",
            message: "Bu kategorinin alt kategorileri bulunduğu için silinemez",
          });
        }

        // Kategoriye ait ürünleri kontrol et
        const categoryProducts = await prisma.product.findMany({
          where: {
            categoryId: id,
            companyId: user.companyId,
            deletedAt: null,
          },
        });

        if (categoryProducts.length > 0) {
          return reply.status(400).send({
            success: false,
            error: "Bad Request",
            message: "Bu kategoriye ait ürünler bulunduğu için silinemez",
          });
        }

        // Soft delete işlemi
        await prisma.category.update({
          where: { id },
          data: {
            deletedAt: new Date(),
            active: false,
            version: { increment: 1 },
          },
        });

        return reply.send({
          success: true,
          message: "Kategori başarıyla silindi",
        });
      } catch (error) {
        fastify.log.error(error);
        return reply.status(500).send({
          success: false,
          error: "Internal Server Error",
          message: "Kategori silinirken bir hata oluştu",
        });
      }
    },
  });

  // Kategori durumunu değiştir (aktif/pasif)
  fastify.route({
    method: "PATCH",
    url: "/:id/status",
    onRequest: [
      fastify.authenticate,
      fastify.authorize(["SUPER_ADMIN", "ADMIN", "BRANCH_MANAGER"]),
    ],
    handler: async (
      request: FastifyRequest<{
        Params: { id: string };
        Body: { active: boolean };
      }>,
      reply: FastifyReply,
    ) => {
      try {
        const user = request.user as JWTPayload;
        const { id } = request.params;
        const { active } = request.body;

        if (!id || typeof id !== "string") {
          return reply.status(400).send({
            success: false,
            error: "Bad Request",
            message: "Geçersiz kategori ID",
          });
        }

        if (typeof active !== "boolean") {
          return reply.status(400).send({
            success: false,
            error: "Bad Request",
            message: "Durum bilgisi boolean olmalıdır",
          });
        }

        // Mevcut kategoriyi kontrol et
        const existingCategory = await prisma.category.findFirst({
          where: {
            id,
            companyId: user.companyId,
            deletedAt: null,
          },
        });

        if (!existingCategory) {
          return reply.status(404).send({
            success: false,
            error: "Not Found",
            message: "Kategori bulunamadı",
          });
        }

        // Durumu güncelle
        const updatedCategory = await prisma.category.update({
          where: { id },
          data: {
            active,
            version: { increment: 1 },
          },
          select: {
            id: true,
            name: true,
            active: true,
          },
        });

        return reply.send({
          success: true,
          data: updatedCategory,
          message: `Kategori ${active ? "aktif" : "pasif"} duruma getirildi`,
        });
      } catch (error) {
        fastify.log.error(error);
        return reply.status(500).send({
          success: false,
          error: "Internal Server Error",
          message: "Kategori durumu güncellenirken bir hata oluştu",
        });
      }
    },
  });
}
