import { PrismaClient } from "@prisma/client";
import bcrypt from "bcryptjs";

const prisma = new PrismaClient();

async function main() {
  console.log("🌱 Veritabanı seed işlemi başlıyor...");

  // 1. Şirket oluştur
  const company = await prisma.company.upsert({
    where: { taxNumber: "1234567890" },
    update: {},
    create: {
      name: "Demo Restoran Zinciri",
      taxNumber: "1234567890",
      taxOffice: "Kadıköy",
      address: "Bağdat Caddesi No:123 Kadıköy/İstanbul",
      phone: "+90 216 123 45 67",
      email: "<EMAIL>",
      website: "https://demorestoran.com",
    },
  });

  console.log("✅ Şirket oluşturuldu:", company.name);

  // 2. Ana şube oluştur
  const branch = await prisma.branch.upsert({
    where: {
      companyId_code: {
        companyId: company.id,
        code: "MAIN01",
      },
    },
    update: {},
    create: {
      companyId: company.id,
      code: "MAIN01",
      name: "<PERSON>kö<PERSON>",
      address: "Bağdat Caddesi No:123 Kadıköy/İstanbul",
      phone: "+90 216 123 45 67",
      email: "<EMAIL>",
      isMainBranch: true,
      openingTime: "08:00",
      closingTime: "23:00",
      workingDays: [1, 2, 3, 4, 5, 6, 7],
    },
  });

  console.log("✅ Şube oluşturuldu:", branch.name);

  // 3. Vergi oranları oluştur
  const tax8 = await prisma.tax.upsert({
    where: {
      companyId_code: {
        companyId: company.id,
        code: "KDV8",
      },
    },
    update: {},
    create: {
      companyId: company.id,
      name: "KDV %8",
      rate: 8.0,
      code: "KDV8",
      type: "VAT",
      isDefault: true,
      isIncluded: true,
    },
  });

  const tax18 = await prisma.tax.upsert({
    where: {
      companyId_code: {
        companyId: company.id,
        code: "KDV18",
      },
    },
    update: {},
    create: {
      companyId: company.id,
      name: "KDV %18",
      rate: 18.0,
      code: "KDV18",
      type: "VAT",
      isIncluded: true,
    },
  });

  console.log("✅ Vergi oranları oluşturuldu");

  // 4. Ödeme yöntemleri oluştur
  const paymentMethods = [
    { name: "Nakit", code: "CASH", type: "CASH" },
    { name: "Kredi Kartı", code: "CC", type: "CREDIT_CARD" },
    { name: "Yemek Kartı", code: "MEAL", type: "MEAL_CARD" },
  ];

  for (const pm of paymentMethods) {
    await prisma.paymentMethod.upsert({
      where: {
        companyId_code: {
          companyId: company.id,
          code: pm.code,
        },
      },
      update: {},
      create: {
        companyId: company.id,
        name: pm.name,
        code: pm.code,
        type: pm.type as any,
      },
    });
  }

  console.log("✅ Ödeme yöntemleri oluşturuldu");

  // 5. Admin kullanıcı oluştur
  const hashedPassword = await bcrypt.hash("admin123", 10);

  const adminUser = await prisma.user.upsert({
    where: { username: "admin" },
    update: {},
    create: {
      companyId: company.id,
      branchId: branch.id,
      username: "admin",
      password: hashedPassword,
      firstName: "Admin",
      lastName: "User",
      email: "<EMAIL>",
      role: "ADMIN",
      employeeCode: "EMP001",
    },
  });

  console.log("✅ Admin kullanıcı oluşturuldu:", adminUser.username);

  // 6. Kategoriler oluştur
  const categories = [
    { name: "Başlangıçlar", color: "#FF6B6B", icon: "🥗" },
    { name: "Ana Yemekler", color: "#4ECDC4", icon: "🍽️" },
    { name: "İçecekler", color: "#45B7D1", icon: "🥤" },
    { name: "Tatlılar", color: "#96CEB4", icon: "🍰" },
  ];

  for (const cat of categories) {
    const existingCategory = await prisma.category.findFirst({
      where: {
        companyId: company.id,
        name: cat.name,
      },
    });

    if (!existingCategory) {
      await prisma.category.create({
        data: {
          companyId: company.id,
          name: cat.name,
          color: cat.color,
          icon: cat.icon,
          active: true,
        },
      });
    }
  }

  console.log("✅ Kategoriler oluşturuldu");

  // 7. Masa alanı ve masalar oluştur
  const tableArea = await prisma.tableArea.create({
    data: {
      branchId: branch.id,
      name: "Ana Salon",
      description: "Restoranın ana yemek salonu",
    },
  });

  // 10 masa oluştur
  for (let i = 1; i <= 10; i++) {
    await prisma.table.create({
      data: {
        branchId: branch.id,
        areaId: tableArea.id,
        number: i.toString(),
        name: `Masa ${i}`,
        capacity: i <= 5 ? 4 : 6,
        positionX: (i % 5) * 100,
        positionY: Math.floor((i - 1) / 5) * 100,
        qrCode: `QR_TABLE_${i}`,
      },
    });
  }

  console.log("✅ Masa alanı ve masalar oluşturuldu");

  console.log("🎉 Seed işlemi tamamlandı!");
  console.log("");
  console.log("📋 Test bilgileri:");
  console.log("   Kullanıcı adı: admin");
  console.log("   Şifre: admin123");
  console.log("   Şirket: Demo Restoran Zinciri");
  console.log("   Şube: Ana Şube - Kadıköy");
}

main()
  .catch((e) => {
    console.error("❌ Seed işlemi başarısız:", e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
