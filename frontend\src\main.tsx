import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "./index.css";
import "./lib/i18n"; // i18n'i başlat
import App from "./App.tsx";

// Browser extension hat<PERSON><PERSON><PERSON><PERSON><PERSON> engelle
window.addEventListener("error", (event) => {
  if (
    event.message.includes("message port closed") ||
    event.message.includes("Extension context invalidated")
  ) {
    event.preventDefault();
    return false;
  }
});

window.addEventListener("unhandledrejection", (event) => {
  if (
    event.reason?.message?.includes("message port closed") ||
    event.reason?.message?.includes("Extension context invalidated")
  ) {
    event.preventDefault();
    return false;
  }
});

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <App />
  </StrictMode>,
);
