import React, { useEffect } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslation } from "react-i18next";
import { Button } from "../ui/Button";
import { Input } from "../ui/Input";
import { Select } from "../ui/Select";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/Card";
import { Modal } from "../ui/Modal";
import {
  taxFormSchema,
  getDefaultTaxFormValues,
  taxToFormData,
} from "../../schemas/taxSchema";
import type { TaxFormData } from "../../schemas/taxSchema";
import { useCreateTax, useUpdateTax } from "../../hooks/useTaxes";
import type { Tax } from "../../types/api";

interface TaxFormProps {
  isOpen: boolean;
  onClose: () => void;
  tax?: Tax | null;
  mode: "create" | "edit";
}

export const TaxForm: React.FC<TaxFormProps> = React.memo(
  ({ isOpen, onClose, tax, mode }) => {
    const { t } = useTranslation();

    // Form setup
    const {
      control,
      handleSubmit,
      reset,
      formState: { errors, isSubmitting },
    } = useForm({
      resolver: zodResolver(taxFormSchema),
      defaultValues: getDefaultTaxFormValues(),
    });

    // Mutations
    const createTax = useCreateTax();
    const updateTax = useUpdateTax();

    // Reset form when tax changes
    useEffect(() => {
      if (isOpen) {
        if (mode === "edit" && tax) {
          reset(taxToFormData(tax));
        } else {
          reset(getDefaultTaxFormValues());
        }
      }
    }, [isOpen, mode, tax, reset]);

    // Form submission
    const onSubmit = async (data: TaxFormData) => {
      try {
        if (mode === "create") {
          await createTax.mutateAsync(data);
        } else if (tax) {
          await updateTax.mutateAsync({
            id: tax.id,
            data,
          });
        }

        onClose();
      } catch (error) {
        // Error is handled by the mutation
      }
    };

    // Tax type options
    const taxTypeOptions = [
      { value: "VAT", label: t("taxes.taxTypes.VAT") },
      { value: "OTV", label: t("taxes.taxTypes.OTV") },
      { value: "OIV", label: t("taxes.taxTypes.OIV") },
      { value: "DAMGA", label: t("taxes.taxTypes.DAMGA") },
    ];

    return (
      <Modal
        isOpen={isOpen}
        onClose={onClose}
        title={mode === "create" ? t("taxes.addTax") : t("taxes.editTax")}
        size="lg"
        className="mx-4 max-h-[90vh] overflow-y-auto"
      >
        <form onSubmit={handleSubmit(onSubmit as any)} className="space-y-6">
          {/* Temel Bilgiler */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">{t("taxes.basicInfo")}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Controller
                  name="name"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      label={t("taxes.taxName")}
                      error={errors.name?.message}
                      required
                      placeholder="KDV %18"
                    />
                  )}
                />

                <Controller
                  name="code"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      label={t("taxes.taxCode")}
                      error={errors.code?.message}
                      required
                      placeholder="KDV18"
                    />
                  )}
                />
              </div>

              <Controller
                name="type"
                control={control}
                render={({ field }) => (
                  <Select
                    {...field}
                    label={t("taxes.taxType")}
                    options={taxTypeOptions}
                    error={errors.type?.message}
                    required
                  />
                )}
              />
            </CardContent>
          </Card>

          {/* Oran Ayarları */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">
                {t("taxes.rateSettings")}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Controller
                name="rate"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="number"
                    step="0.01"
                    min="0"
                    max="100"
                    label={t("taxes.taxRate")}
                    error={errors.rate?.message}
                    required
                    onChange={(e) =>
                      field.onChange(Number(e.target.value) || 0)
                    }
                  />
                )}
              />
            </CardContent>
          </Card>

          {/* Durum Ayarları */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">
                {t("taxes.statusSettings")}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-col space-y-4">
                <div className="flex items-center space-x-2">
                  <Controller
                    name="isDefault"
                    control={control}
                    render={({ field }) => (
                      <input
                        type="checkbox"
                        checked={field.value}
                        onChange={field.onChange}
                        className="rounded border-neutral-300 dark:border-neutral-600"
                      />
                    )}
                  />
                  <div className="flex flex-col">
                    <label className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                      {t("taxes.isDefault")}
                    </label>
                    <span className="text-xs text-neutral-500">
                      {t("taxes.defaultTaxInfo")}
                    </span>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Controller
                    name="isIncluded"
                    control={control}
                    render={({ field }) => (
                      <input
                        type="checkbox"
                        checked={field.value}
                        onChange={field.onChange}
                        className="rounded border-neutral-300 dark:border-neutral-600"
                      />
                    )}
                  />
                  <div className="flex flex-col">
                    <label className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                      {t("taxes.isIncluded")}
                    </label>
                    <span className="text-xs text-neutral-500">
                      {t("taxes.includedTaxInfo")}
                    </span>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Controller
                    name="active"
                    control={control}
                    render={({ field }) => (
                      <input
                        type="checkbox"
                        checked={field.value}
                        onChange={field.onChange}
                        className="rounded border-neutral-300 dark:border-neutral-600"
                      />
                    )}
                  />
                  <label className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                    {t("taxes.active")}
                  </label>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Form Actions */}
          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              {t("common.cancel")}
            </Button>
            <Button
              type="submit"
              loading={isSubmitting}
              disabled={isSubmitting}
            >
              {mode === "create" ? t("common.create") : t("common.update")}
            </Button>
          </div>
        </form>
      </Modal>
    );
  },
);
