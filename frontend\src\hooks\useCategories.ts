import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { CategoryService } from "../services/categoryService";
import { useToast } from "../providers/ToastProvider";
import type {
  CategoryQueryParams,
  CreateCategoryRequest,
  UpdateCategoryRequest,
} from "../types/api";

// Query keys
export const categoryKeys = {
  all: ["categories"] as const,
  lists: () => [...categoryKeys.all, "list"] as const,
  list: (params?: CategoryQueryParams) =>
    [...categoryKeys.lists(), params] as const,
  hierarchy: () => [...categoryKeys.all, "hierarchy"] as const,
  roots: () => [...categoryKeys.all, "roots"] as const,
  details: () => [...categoryKeys.all, "detail"] as const,
  detail: (id: string) => [...categoryKeys.details(), id] as const,
  stats: () => [...categoryKeys.all, "stats"] as const,
};

// Category Queries
export const useCategories = (params?: CategoryQueryParams) => {
  return useQuery({
    queryKey: categoryKeys.list(params),
    queryFn: () => CategoryService.getCategories(params),
    select: (response) => response.data,
  });
};

export const useCategoriesHierarchy = () => {
  return useQuery({
    queryKey: categoryKeys.hierarchy(),
    queryFn: () => CategoryService.getCategoriesHierarchy(),
    select: (response) => response.data,
  });
};

export const useRootCategories = () => {
  return useQuery({
    queryKey: categoryKeys.roots(),
    queryFn: () => CategoryService.getRootCategories(),
    select: (response) => response.data,
  });
};

export const useCategory = (id: string) => {
  return useQuery({
    queryKey: categoryKeys.detail(id),
    queryFn: () => CategoryService.getCategory(id),
    select: (response) => response.data,
    enabled: !!id,
  });
};

export const useSubCategories = (parentId: string) => {
  return useQuery({
    queryKey: categoryKeys.list({ parentId }),
    queryFn: () => CategoryService.getSubCategories(parentId),
    select: (response) => response.data,
    enabled: !!parentId,
  });
};

// Category Mutations
export const useCreateCategory = () => {
  const queryClient = useQueryClient();
  const { success, error } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: (data: CreateCategoryRequest) =>
      CategoryService.createCategory(data),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: categoryKeys.all });
      success({
        title: t("categories.categoryAdded"),
        message: response.message,
      });
    },
    onError: (err: any) => {
      error({
        title: t("common.error"),
        message: err.message || t("categories.createError"),
      });
    },
  });
};

export const useUpdateCategory = () => {
  const queryClient = useQueryClient();
  const { success, error } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateCategoryRequest }) =>
      CategoryService.updateCategory(id, data),
    onSuccess: (response, { id }) => {
      queryClient.invalidateQueries({ queryKey: categoryKeys.all });
      queryClient.invalidateQueries({ queryKey: categoryKeys.detail(id) });
      success({
        title: t("categories.categoryUpdated"),
        message: response.message,
      });
    },
    onError: (err: any) => {
      error({
        title: t("common.error"),
        message: err.message || t("categories.updateError"),
      });
    },
  });
};

export const useDeleteCategory = () => {
  const queryClient = useQueryClient();
  const { success, error } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: (id: string) => CategoryService.deleteCategory(id),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: categoryKeys.all });
      success({
        title: t("categories.categoryDeleted"),
        message: response.message,
      });
    },
    onError: (err: any) => {
      error({
        title: t("common.error"),
        message: err.message || t("categories.deleteError"),
      });
    },
  });
};

export const useToggleCategoryStatus = () => {
  const queryClient = useQueryClient();
  const { success, error } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: (id: string) => CategoryService.toggleCategoryStatus(id),
    onSuccess: (response, id) => {
      queryClient.invalidateQueries({ queryKey: categoryKeys.all });
      queryClient.invalidateQueries({ queryKey: categoryKeys.detail(id) });
      success({
        title: t("categories.statusUpdated"),
        message: response.message,
      });
    },
    onError: (err: any) => {
      error({
        title: t("common.error"),
        message: err.message || t("categories.statusUpdateError"),
      });
    },
  });
};

// Utility hooks
export const useCategoryPath = (id: string) => {
  return useQuery({
    queryKey: [...categoryKeys.all, "path", id],
    queryFn: () => CategoryService.getCategoryPath(id),
    select: (response) => response.data,
    enabled: !!id,
  });
};

export const useCategoryProductCount = (id: string) => {
  return useQuery({
    queryKey: [...categoryKeys.all, "productCount", id],
    queryFn: () => CategoryService.getProductCount(id),
    select: (response) => response.data,
    enabled: !!id,
  });
};

export const useColorSuggestions = () => {
  return useQuery({
    queryKey: [...categoryKeys.all, "colorSuggestions"],
    queryFn: () => CategoryService.getColorSuggestions(),
    select: (response) => response.data,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useIconSuggestions = () => {
  return useQuery({
    queryKey: [...categoryKeys.all, "iconSuggestions"],
    queryFn: () => CategoryService.getIconSuggestions(),
    select: (response) => response.data,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};
